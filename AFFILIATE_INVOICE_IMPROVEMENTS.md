# Affiliate Invoice Improvements v2 - Frontend Implementation

This document describes the frontend implementation for the Affiliate Invoice improvements v2 feature, covering the reminder tree about debt functionality.

## Overview

The implementation adds three main features to the affiliate CRM finances section:

1. **BAPR-469**: Unpaid Amount card in the finances section
2. **BAPR-470**: Unsettled Amount column in the invoices table
3. **BAPR-471**: Status indicators for invoices (red button for unsettled, green/blue labels for new)

## Implementation Details

### 1. BAPR-469: Unpaid Amount Card

**Location**: `src/modules/finance/merchant-balance-info/`

**Changes**:
- Added `UNPAID_AMOUNT_TITLE` and `UNPAID_AMOUNT_DESCRIPTION` to localization keys
- Modified `MerchantBalanceInfo.hooks.ts` to include unpaid amount card
- Uses mock data from `getMockMerchantBalance()` until backend is ready

**Files Modified**:
- `src/shared/constants/localization-keys/finance.ts`
- `src/modules/finance/merchant-balance-info/MerchantBalanceInfo.hooks.ts`

### 2. BAPR-470: Unsettled Amount Column

**Location**: `src/modules/finance/invoices-table/`

**Changes**:
- Added `INVOICES_TABLE_COLUMN_UNSETTLED_AMOUNT` localization key
- Added new column to the invoices table
- Modified `InvoicesCollectionByPage.tsx` to display unsettled amounts
- Uses mock data from `getInvoiceUnsettledAmount()` for each invoice

**Files Modified**:
- `src/shared/constants/localization-keys/finance.ts`
- `src/modules/finance/invoices-table/InvoicesTable.tsx`
- `src/modules/finance/invoices-table/components/InvoicesCollectionByPage.tsx`

### 3. BAPR-471: Status Indicators

**Location**: `src/modules/finance/invoices-table/components/`

**Changes**:
- Created new `InvoiceStatusIndicators` component
- Added status indicators column to invoices table
- Displays red button for unsettled invoices
- Displays green or blue "New" badges for new invoices
- Uses mock data to determine invoice status

**Files Created**:
- `src/modules/finance/invoices-table/components/InvoiceStatusIndicators.tsx`

**Files Modified**:
- `src/modules/finance/invoices-table/components/index.ts`
- `src/modules/finance/invoices-table/InvoicesTable.tsx`
- `src/modules/finance/invoices-table/components/InvoicesCollectionByPage.tsx`

### 4. Mock Data Implementation

**Location**: `src/modules/finance/utils/mockData.ts`

Since the backend is not ready yet, mock data utilities were created to simulate the new fields:

**Mock Data Features**:
- `getMockInvoiceData()`: Returns mock invoice data with unsettled amount, status, and new flag
- `getMockMerchantBalance()`: Returns mock merchant balance with unpaid amount
- `isInvoiceUnsettled()`: Checks if an invoice has unsettled amount
- `isInvoiceNew()`: Checks if an invoice is marked as new
- `getInvoiceUnsettledAmount()`: Gets the unsettled amount for an invoice

**Mock Data Structure**:
```typescript
interface MockInvoiceData {
  id: number;
  unsettled_amount: number;
  status: 'paid' | 'unpaid' | 'partially_paid';
  is_new: boolean;
}

interface MockMerchantBalance {
  unpaid_amount: number;
}
```

### 5. Localization Keys Added

```typescript
// Added to LocizeFinanceKeys
UNPAID_AMOUNT_TITLE: 'unpaid-amount-title',
UNPAID_AMOUNT_DESCRIPTION: 'unpaid-amount-description',
INVOICES_TABLE_COLUMN_UNSETTLED_AMOUNT: 'invoices-table-column-unsettled-amount',
INVOICES_TABLE_STATUS_NEW: 'invoices-table-status-new',
INVOICES_TABLE_STATUS_UNSETTLED: 'invoices-table-status-unsettled',
```

## Testing

- Created comprehensive unit tests for mock data utilities
- All TypeScript checks pass
- Development server runs without errors

**Test File**: `src/modules/finance/utils/mockData.test.ts`

## Future Backend Integration

When the backend is ready, the following changes will be needed:

1. **GraphQL Schema Updates**:
   - Add `unpaid_amount` field to merchant balance query
   - Add `unsettled_amount`, `status`, and `is_new` fields to invoice queries

2. **Remove Mock Data**:
   - Replace mock data calls with actual GraphQL data
   - Remove `src/modules/finance/utils/mockData.ts`
   - Update components to use real data from GraphQL responses

3. **Update Queries**:
   - Modify `src/modules/finance/merchant-balance-info/api/queries.graphql`
   - Modify `src/modules/finance/invoices-table/api/queries.graphql`
   - Run `npm run codegen` to regenerate TypeScript types

## Visual Design

- **Unpaid Amount Card**: Follows the same design pattern as existing balance cards
- **Unsettled Amount Column**: Standard table column with currency formatting
- **Status Indicators**: 
  - Red button for unsettled invoices
  - Green or blue badges for new invoices (randomly assigned for demo)
  - Positioned in a dedicated status column

## Notes

- All changes are backward compatible
- Mock data provides realistic test scenarios
- Implementation follows existing code patterns and conventions
- Ready for backend integration when available
