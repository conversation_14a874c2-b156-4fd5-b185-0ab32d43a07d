import { NetworkStatus, useReactiveVar } from '@apollo/client';
import { useContext, useMemo } from 'react';
import { useFormattedDate } from 'shared/components/date-range/common';
import { LocizeFinanceKeys } from 'shared/constants/localization-keys';
import { useMerchantDetails } from 'shared/hooks/merchant';
import { GlobalStateContext } from 'shared/hooks/state';
import { useFormattedAmount } from 'shared/hooks/utils';

import { transactionsTableState } from '../transactions-table/TransactionsTable.utils';
import { getMockMerchantBalance } from '../utils/mockData';
import { useMerchantBalanceQuery } from './api';

type CardData = {
  title: string;
  isIncreased: boolean;
  balance: string;
  description: string;
  isVisible?: boolean;
};

export const useMerchantBalanceCardsData = () => {
  const { merchantId } = useContext(GlobalStateContext);
  const { dateRange } = useReactiveVar(transactionsTableState);
  const {
    networkStatus: merchantDetailsQueryNetworkStatus,
    data: merchantData,
  } = useMerchantDetails({
    notifyOnNetworkStatusChange: true,
  });
  const dateRangeTo = useFormattedDate(
    dateRange.to ? dateRange.to : new Date(),
  );

  const { data, networkStatus: merchantBalanceQueryNetworkStatus } =
    useMerchantBalanceQuery({
      notifyOnNetworkStatusChange: true,
      skip: !merchantId,
      variables: {
        merchantId: merchantId ?? -1,
        balanceAt: dateRangeTo,
      },
    });

  const principalBalanceFormatted = useFormattedAmount(
    data?.balance?.pending_principal || 0,
  );
  const bonusBalanceFormatted = useFormattedAmount(
    data?.balance?.pending_bonus || 0,
  );

  // Mock data for unpaid amount until backend is ready
  const mockMerchantBalance = getMockMerchantBalance();
  const unpaidAmountFormatted = useFormattedAmount(
    mockMerchantBalance.unpaid_amount,
  );

  const balanceCardsData: Array<CardData> = useMemo(
    () => [
      {
        title: LocizeFinanceKeys.BALANCE_TITLE,
        isIncreased:
          !!data?.balance?.pending_principal &&
          data?.balance?.pending_principal > 0,
        balance: principalBalanceFormatted,
        description: LocizeFinanceKeys.BALANCE_DESCRIPTION,
      },
      {
        title: LocizeFinanceKeys.BONUS_TITLE,
        isIncreased:
          !!data?.balance?.pending_bonus && data?.balance?.pending_bonus > 0,
        balance: bonusBalanceFormatted,
        description: LocizeFinanceKeys.BONUS_DESCRIPTION,
        isVisible:
          !!merchantData?.merchant?.settings?.bonus_pct &&
          merchantData?.merchant?.settings?.bonus_pct > 0,
      },
      {
        title: LocizeFinanceKeys.UNPAID_AMOUNT_TITLE,
        isIncreased: mockMerchantBalance.unpaid_amount > 0,
        balance: unpaidAmountFormatted,
        description: LocizeFinanceKeys.UNPAID_AMOUNT_DESCRIPTION,
      },
    ],
    [
      data?.balance?.pending_principal,
      data?.balance?.pending_bonus,
      principalBalanceFormatted,
      bonusBalanceFormatted,
      merchantData?.merchant?.settings?.bonus_pct,
      mockMerchantBalance.unpaid_amount,
      unpaidAmountFormatted,
    ],
  );

  return {
    balanceCardsData,
    isFetched:
      merchantBalanceQueryNetworkStatus === NetworkStatus.ready &&
      merchantDetailsQueryNetworkStatus === NetworkStatus.ready,
  };
};
