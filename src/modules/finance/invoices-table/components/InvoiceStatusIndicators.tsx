import { <PERSON>ge, <PERSON>ton, HStack } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import {
  LocizeFinanceKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';

import {
  getMockInvoiceData,
  isInvoiceNew,
  isInvoiceUnsettled,
} from '../../utils/mockData';

type Props = {
  invoiceId: number;
};

export const InvoiceStatusIndicators = ({ invoiceId }: Props) => {
  const { t } = useTranslation(LocizeNamespaces.FINANCE);
  const mockData = getMockInvoiceData(invoiceId);
  const isNew = isInvoiceNew(invoiceId);
  const isUnsettled = isInvoiceUnsettled(invoiceId);

  return (
    <HStack spacing={2}>
      {/* New invoice indicator - green or blue badge */}
      {isNew && (
        <Badge
          colorScheme={Math.random() > 0.5 ? 'green' : 'blue'}
          variant="solid"
          fontSize="xs"
          px={2}
          py={1}
          borderRadius="md"
        >
          {t(LocizeFinanceKeys.INVOICES_TABLE_STATUS_NEW)}
        </Badge>
      )}
      
      {/* Unsettled invoice indicator - red button */}
      {isUnsettled && (
        <Button
          size="xs"
          colorScheme="red"
          variant="solid"
          fontSize="xs"
          px={2}
          py={1}
          height="auto"
          minHeight="auto"
          borderRadius="md"
        >
          {t(LocizeFinanceKeys.INVOICES_TABLE_STATUS_UNSETTLED)}
        </Button>
      )}
    </HStack>
  );
};
