// Mock data utilities for affiliate invoice improvements
// This file provides mock data for new fields until backend is ready

export interface MockInvoiceData {
  id: number;
  unsettled_amount: number;
  status: 'paid' | 'unpaid' | 'partially_paid';
  is_new: boolean;
}

export interface MockMerchantBalance {
  unpaid_amount: number;
}

// Mock data for invoices - simulating different states
const MOCK_INVOICE_DATA: Record<number, MockInvoiceData> = {
  1: { id: 1, unsettled_amount: 150.50, status: 'unpaid', is_new: true },
  2: { id: 2, unsettled_amount: 0, status: 'paid', is_new: false },
  3: { id: 3, unsettled_amount: 75.25, status: 'partially_paid', is_new: false },
  4: { id: 4, unsettled_amount: 200.00, status: 'unpaid', is_new: true },
  5: { id: 5, unsettled_amount: 0, status: 'paid', is_new: false },
  6: { id: 6, unsettled_amount: 300.75, status: 'unpaid', is_new: false },
  7: { id: 7, unsettled_amount: 50.00, status: 'partially_paid', is_new: true },
  8: { id: 8, unsettled_amount: 0, status: 'paid', is_new: false },
  9: { id: 9, unsettled_amount: 125.30, status: 'unpaid', is_new: false },
  10: { id: 10, unsettled_amount: 0, status: 'paid', is_new: false },
};

// Mock merchant balance data
const MOCK_MERCHANT_BALANCE: MockMerchantBalance = {
  unpaid_amount: 1250.75, // Total unpaid amount for the merchant
};

/**
 * Get mock invoice data by invoice ID
 */
export const getMockInvoiceData = (invoiceId: number): MockInvoiceData => {
  return MOCK_INVOICE_DATA[invoiceId] || {
    id: invoiceId,
    unsettled_amount: Math.random() * 500, // Random amount for unknown IDs
    status: Math.random() > 0.5 ? 'unpaid' : 'paid',
    is_new: Math.random() > 0.7, // 30% chance of being new
  };
};

/**
 * Get mock merchant balance data
 */
export const getMockMerchantBalance = (): MockMerchantBalance => {
  return MOCK_MERCHANT_BALANCE;
};

/**
 * Check if an invoice is unsettled (has unpaid amount)
 */
export const isInvoiceUnsettled = (invoiceId: number): boolean => {
  const mockData = getMockInvoiceData(invoiceId);
  return mockData.unsettled_amount > 0;
};

/**
 * Check if an invoice is new
 */
export const isInvoiceNew = (invoiceId: number): boolean => {
  const mockData = getMockInvoiceData(invoiceId);
  return mockData.is_new;
};

/**
 * Get unsettled amount for an invoice
 */
export const getInvoiceUnsettledAmount = (invoiceId: number): number => {
  const mockData = getMockInvoiceData(invoiceId);
  return mockData.unsettled_amount;
};
