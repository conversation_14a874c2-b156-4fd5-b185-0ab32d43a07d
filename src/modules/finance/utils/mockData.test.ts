import { describe, expect, it } from 'vitest';

import {
  getMockInvoiceData,
  getMockMerchantBalance,
  getInvoiceUnsettledAmount,
  isInvoiceNew,
  isInvoiceUnsettled,
} from './mockData';

describe('mockData utilities', () => {
  describe('getMockInvoiceData', () => {
    it('should return mock data for known invoice IDs', () => {
      const mockData = getMockInvoiceData(1);
      expect(mockData).toEqual({
        id: 1,
        unsettled_amount: 150.50,
        status: 'unpaid',
        is_new: true,
      });
    });

    it('should return generated data for unknown invoice IDs', () => {
      const mockData = getMockInvoiceData(999);
      expect(mockData.id).toBe(999);
      expect(typeof mockData.unsettled_amount).toBe('number');
      expect(['paid', 'unpaid', 'partially_paid']).toContain(mockData.status);
      expect(typeof mockData.is_new).toBe('boolean');
    });
  });

  describe('getMockMerchantBalance', () => {
    it('should return mock merchant balance data', () => {
      const balance = getMockMerchantBalance();
      expect(balance).toEqual({
        unpaid_amount: 1250.75,
      });
    });
  });

  describe('getInvoiceUnsettledAmount', () => {
    it('should return unsettled amount for invoice', () => {
      const amount = getInvoiceUnsettledAmount(1);
      expect(amount).toBe(150.50);
    });
  });

  describe('isInvoiceUnsettled', () => {
    it('should return true for invoices with unsettled amount', () => {
      expect(isInvoiceUnsettled(1)).toBe(true); // has 150.50 unsettled
    });

    it('should return false for invoices without unsettled amount', () => {
      expect(isInvoiceUnsettled(2)).toBe(false); // has 0 unsettled
    });
  });

  describe('isInvoiceNew', () => {
    it('should return true for new invoices', () => {
      expect(isInvoiceNew(1)).toBe(true);
    });

    it('should return false for old invoices', () => {
      expect(isInvoiceNew(2)).toBe(false);
    });
  });
});
